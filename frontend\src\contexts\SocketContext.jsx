import React, { createContext, useContext, useEffect, useRef, useState, useCallback } from 'react';
import { io } from 'socket.io-client';
import { useAuth } from './AuthContext';

const SocketContext = createContext({});

export const useSocket = () => {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};

export const SocketProvider = ({ children }) => {
  const { user, getToken, isAuthenticated } = useAuth();
  const socketRef = useRef(null);
  const [isConnected, setIsConnected] = useState(false);
  const [onlineUsers, setOnlineUsers] = useState(new Set());
  const [typingUsers, setTypingUsers] = useState(new Map()); // chatId -> Set of userIds
  const [connectionAttempts, setConnectionAttempts] = useState(0);
  const [isConnecting, setIsConnecting] = useState(false);
  
  // Event listeners storage
  const listenersRef = useRef(new Map());
  const reconnectTimeoutRef = useRef(null);

  // Connect to socket with rate limiting
  const connect = useCallback(async () => {
    if (!isAuthenticated || isConnecting || socketRef.current?.connected) {
      return;
    }

    // Rate limiting: max 5 attempts per minute
    if (connectionAttempts >= 5) {
      console.warn('Too many connection attempts, waiting...');
      return;
    }

    setIsConnecting(true);
    setConnectionAttempts(prev => prev + 1);

    try {
      const token = await getToken();
      if (!token) {
        setIsConnecting(false);
        return;
      }

      // Disconnect existing socket if any
      if (socketRef.current) {
        socketRef.current.disconnect();
        socketRef.current = null;
      }

      const socket = io(import.meta.env.VITE_API_BASE_URL?.replace('/api', '') || 'http://localhost:5000', {
        auth: { token },
        transports: ['websocket', 'polling'],
        timeout: 10000,
        retries: 3,
        forceNew: true, // Force new connection
      });

      socketRef.current = socket;

      // Connection events
      socket.on('connect', () => {
        console.log('Socket connected:', socket.id);
        setIsConnected(true);
        setIsConnecting(false);
        setConnectionAttempts(0); // Reset on successful connection
      });

      socket.on('disconnect', (reason) => {
        console.log('Socket disconnected:', reason);
        setIsConnected(false);
        setIsConnecting(false);
        setOnlineUsers(new Set());
        setTypingUsers(new Map());
        
        // Auto-reconnect for certain disconnect reasons
        if (reason === 'io server disconnect' || reason === 'transport close') {
          scheduleReconnect();
        }
      });

      socket.on('connect_error', (error) => {
        console.error('Socket connection error:', error);
        setIsConnected(false);
        setIsConnecting(false);
        scheduleReconnect();
      });

      // User status events
      socket.on('user:online', (data) => {
        setOnlineUsers(prev => new Set([...prev, data.userId]));
      });

      socket.on('user:offline', (data) => {
        setOnlineUsers(prev => {
          const newSet = new Set(prev);
          newSet.delete(data.userId);
          return newSet;
        });
      });

      // Typing events
      socket.on('typing:start', (data) => {
        setTypingUsers(prev => {
          const newMap = new Map(prev);
          if (!newMap.has(data.chatId)) {
            newMap.set(data.chatId, new Set());
          }
          newMap.get(data.chatId).add(data.userId);
          return newMap;
        });
      });

      socket.on('typing:stop', (data) => {
        setTypingUsers(prev => {
          const newMap = new Map(prev);
          if (newMap.has(data.chatId)) {
            newMap.get(data.chatId).delete(data.userId);
            if (newMap.get(data.chatId).size === 0) {
              newMap.delete(data.chatId);
            }
          }
          return newMap;
        });
      });

      // Error handling
      socket.on('error', (error) => {
        console.error('Socket error:', error);
      });

    } catch (error) {
      console.error('Socket connection failed:', error);
      setIsConnecting(false);
      scheduleReconnect();
    }
  }, [isAuthenticated, getToken, connectionAttempts, isConnecting]);

  // Schedule reconnection with exponential backoff
  const scheduleReconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }

    const delay = Math.min(1000 * Math.pow(2, connectionAttempts), 30000); // Max 30 seconds
    console.log(`Scheduling reconnect in ${delay}ms`);
    
    reconnectTimeoutRef.current = setTimeout(() => {
      if (isAuthenticated && !socketRef.current?.connected) {
        connect();
      }
    }, delay);
  }, [connectionAttempts, isAuthenticated, connect]);

  // Disconnect socket
  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (socketRef.current) {
      socketRef.current.disconnect();
      socketRef.current = null;
      setIsConnected(false);
      setIsConnecting(false);
      setOnlineUsers(new Set());
      setTypingUsers(new Map());
      listenersRef.current.clear();
    }
  }, []);

  // Reset connection attempts every minute
  useEffect(() => {
    const interval = setInterval(() => {
      setConnectionAttempts(0);
    }, 60000);

    return () => clearInterval(interval);
  }, []);

  // Auto-connect when authenticated
  useEffect(() => {
    if (isAuthenticated && !socketRef.current?.connected && !isConnecting) {
      connect();
    } else if (!isAuthenticated) {
      disconnect();
    }
  }, [isAuthenticated, connect, disconnect, isConnecting]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  // Socket utility functions
  const joinChat = useCallback((chatId) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('chat:join', { chatId });
    }
  }, []);

  const leaveChat = useCallback((chatId) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('chat:leave', { chatId });
    }
  }, []);

  const sendMessage = useCallback((chatId, text, tempId) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('message:send', { chatId, text, tempId });
    }
  }, []);

  const markMessagesSeen = useCallback((chatId, messageIds) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('message:seen', { chatId, messageIds });
    }
  }, []);

  const startTyping = useCallback((chatId) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('typing:start', { chatId });
    }
  }, []);

  const stopTyping = useCallback((chatId) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('typing:stop', { chatId });
    }
  }, []);

  const addEventListener = useCallback((event, handler) => {
    if (socketRef.current) {
      socketRef.current.on(event, handler);
      
      // Store listener for cleanup
      if (!listenersRef.current.has(event)) {
        listenersRef.current.set(event, new Set());
      }
      listenersRef.current.get(event).add(handler);
    }
  }, []);

  const removeEventListener = useCallback((event, handler) => {
    if (socketRef.current) {
      socketRef.current.off(event, handler);
      
      // Remove from stored listeners
      if (listenersRef.current.has(event)) {
        listenersRef.current.get(event).delete(handler);
      }
    }
  }, []);

  const getTypingUsers = useCallback((chatId) => {
    return typingUsers.get(chatId) || new Set();
  }, [typingUsers]);

  const isUserOnline = useCallback((userId) => {
    return onlineUsers.has(userId);
  }, [onlineUsers]);

  const value = {
    socket: socketRef.current,
    isConnected,
    isConnecting,
    onlineUsers,
    typingUsers,
    connect,
    disconnect,
    joinChat,
    leaveChat,
    sendMessage,
    markMessagesSeen,
    startTyping,
    stopTyping,
    addEventListener,
    removeEventListener,
    getTypingUsers,
    isUserOnline,
  };

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  );
};
