const express = require('express');
const Chat = require('../models/Chat');
const Message = require('../models/Message');
const User = require('../models/User');

const router = express.Router();

/**
 * GET /api/chats
 * Get all chats for the current user
 */
router.get('/', async (req, res) => {
  try {
    const userId = req.user._id;
    
    const chats = await Chat.findUserChats(userId);
    
    // Format response with additional info
    const formattedChats = chats.map(chat => {
      const chatObj = chat.toObject();
      
      // For direct chats, get the other participant's info
      if (!chat.isGroup) {
        const otherMember = chat.members.find(member => 
          member.user._id.toString() !== userId.toString()
        );
        
        if (otherMember) {
          chatObj.name = otherMember.user.name;
          chatObj.avatar = otherMember.user.avatar;
          chatObj.isOnline = otherMember.user.isOnline;
          chatObj.lastSeen = otherMember.user.lastSeen;
        }
      }
      
      return chatObj;
    });
    
    res.status(200).json({
      success: true,
      chats: formattedChats,
      count: formattedChats.length
    });
    
  } catch (error) {
    console.error('Get chats error:', error.message);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get chats'
    });
  }
});

/**
 * POST /api/chats/direct
 * Create or get existing direct chat with another user
 */
router.post('/direct', async (req, res) => {
  try {
    const { userId: otherUserId } = req.body;
    const currentUserId = req.user._id;
    
    if (!otherUserId) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'User ID is required'
      });
    }
    
    if (otherUserId === currentUserId.toString()) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Cannot create chat with yourself'
      });
    }
    
    // Check if other user exists
    const otherUser = await User.findById(otherUserId);
    if (!otherUser) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'User not found'
      });
    }
    
    // Check if direct chat already exists
    let chat = await Chat.findDirectChat(currentUserId, otherUserId);
    
    if (!chat) {
      // Create new direct chat
      console.log('Creating new direct chat between:', currentUserId.toString(), 'and', otherUserId);
      chat = await Chat.createDirectChat(currentUserId, otherUserId);
      console.log('Created chat with ID:', chat._id.toString());

      // Ensure the chat is saved and refresh from database
      await chat.save();
      chat = await Chat.findById(chat._id);
    } else {
      console.log('Found existing direct chat:', chat._id.toString());
    }

    // Populate the chat with user info
    await chat.populate('members.user', 'name email avatar isOnline lastSeen');
    
    const chatObj = chat.toObject();
    
    // Set chat name and avatar to the other user's info
    const otherMember = chat.members.find(member => 
      member.user._id.toString() !== currentUserId.toString()
    );
    
    if (otherMember) {
      chatObj.name = otherMember.user.name;
      chatObj.avatar = otherMember.user.avatar;
      chatObj.isOnline = otherMember.user.isOnline;
      chatObj.lastSeen = otherMember.user.lastSeen;
    }
    
    res.status(200).json({
      success: true,
      chat: chatObj
    });
    
  } catch (error) {
    console.error('Create direct chat error:', error.message);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to create direct chat'
    });
  }
});

/**
 * POST /api/chats/group
 * Create a new group chat
 */
router.post('/group', async (req, res) => {
  try {
    const { name, description, memberIds = [] } = req.body;
    const currentUserId = req.user._id;
    
    if (!name || name.trim().length === 0) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Group name is required'
      });
    }
    
    // Validate member IDs
    const validMemberIds = [];
    for (const memberId of memberIds) {
      if (memberId !== currentUserId.toString()) {
        const user = await User.findById(memberId);
        if (user) {
          validMemberIds.push(memberId);
        }
      }
    }
    
    // Create group chat
    const chat = new Chat({
      isGroup: true,
      name: name.trim(),
      description: description?.trim() || '',
      members: [
        { user: currentUserId, role: 'admin' }, // Creator is admin
        ...validMemberIds.map(id => ({ user: id, role: 'member' }))
      ]
    });
    
    await chat.save();
    await chat.populate('members.user', 'name email avatar isOnline lastSeen');
    
    res.status(201).json({
      success: true,
      chat: chat.toObject()
    });
    
  } catch (error) {
    console.error('Create group chat error:', error.message);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to create group chat'
    });
  }
});

/**
 * GET /api/chats/:chatId
 * Get specific chat details
 */
router.get('/:chatId', async (req, res) => {
  try {
    const { chatId } = req.params;
    const userId = req.user._id;
    
    const chat = await Chat.findById(chatId)
      .populate('members.user', 'name email avatar isOnline lastSeen')
      .populate('lastMessage');
    
    if (!chat) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Chat not found'
      });
    }
    
    // Check if user is a member of this chat
    console.log('Checking membership for user:', userId.toString());
    console.log('Chat members:', chat.members.map(m => ({ user: m.user._id?.toString() || m.user.toString(), role: m.role })));

    if (!chat.isMember(userId)) {
      console.log('User is not a member of chat:', chatId);
      return res.status(403).json({
        error: 'Forbidden',
        message: 'You are not a member of this chat'
      });
    }

    console.log('User is a member of chat:', chatId);
    
    const chatObj = chat.toObject();
    
    // For direct chats, set name and avatar to other user's info
    if (!chat.isGroup) {
      const otherMember = chat.members.find(member => 
        member.user._id.toString() !== userId.toString()
      );
      
      if (otherMember) {
        chatObj.name = otherMember.user.name;
        chatObj.avatar = otherMember.user.avatar;
        chatObj.isOnline = otherMember.user.isOnline;
        chatObj.lastSeen = otherMember.user.lastSeen;
      }
    }
    
    res.status(200).json({
      success: true,
      chat: chatObj
    });
    
  } catch (error) {
    console.error('Get chat error:', error.message);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to get chat'
    });
  }
});

/**
 * PUT /api/chats/:chatId
 * Update chat details (group chats only)
 */
router.put('/:chatId', async (req, res) => {
  try {
    const { chatId } = req.params;
    const { name, description, avatar } = req.body;
    const userId = req.user._id;
    
    const chat = await Chat.findById(chatId);
    
    if (!chat) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Chat not found'
      });
    }
    
    if (!chat.isGroup) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Cannot update direct chat details'
      });
    }
    
    // Check if user is admin
    if (!chat.isAdmin(userId)) {
      return res.status(403).json({
        error: 'Forbidden',
        message: 'Only admins can update group details'
      });
    }
    
    // Update fields
    if (name && name.trim().length > 0) {
      chat.name = name.trim();
    }
    
    if (description !== undefined) {
      chat.description = description.trim();
    }
    
    if (avatar !== undefined) {
      chat.avatar = avatar;
    }
    
    await chat.save();
    await chat.populate('members.user', 'name email avatar isOnline lastSeen');
    
    res.status(200).json({
      success: true,
      message: 'Chat updated successfully',
      chat: chat.toObject()
    });
    
  } catch (error) {
    console.error('Update chat error:', error.message);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to update chat'
    });
  }
});

module.exports = router;
