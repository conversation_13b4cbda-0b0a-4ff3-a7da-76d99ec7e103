const mongoose = require('mongoose');

const chatSchema = new mongoose.Schema({
  // Chat type and basic info
  isGroup: {
    type: Boolean,
    default: false
  },
  
  name: {
    type: String,
    trim: true,
    maxlength: 100,
    // Required only for group chats
    required: function() {
      return this.isGroup;
    }
  },
  
  description: {
    type: String,
    trim: true,
    maxlength: 500,
    default: ''
  },
  
  avatar: {
    type: String,
    default: null // URL to group avatar
  },
  
  // Chat members
  members: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    role: {
      type: String,
      enum: ['admin', 'member'],
      default: 'member'
    },
    joinedAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // Chat settings
  settings: {
    allowMembersToAddOthers: {
      type: Boolean,
      default: true
    },
    onlyAdminsCanSend: {
      type: Boolean,
      default: false
    }
  },
  
  // Last message reference for quick access
  lastMessage: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Message',
    default: null
  },
  
  lastActivity: {
    type: Date,
    default: Date.now
  },
  
  // For direct chats, store both user IDs for easy querying
  participants: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }]
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      delete ret.__v;
      return ret;
    }
  }
});

// Indexes for better performance
chatSchema.index({ 'members.user': 1 });
chatSchema.index({ participants: 1 });
chatSchema.index({ lastActivity: -1 });
chatSchema.index({ isGroup: 1 });

// Compound index for direct chat lookup
chatSchema.index({ 
  isGroup: 1, 
  participants: 1 
});

// Virtual for member count
chatSchema.virtual('memberCount').get(function() {
  return this.members.length;
});

// Instance methods
chatSchema.methods.addMember = function(userId, role = 'member') {
  const existingMember = this.members.find(member => {
    // Handle both populated and non-populated user references
    const memberUserId = member.user._id || member.user;
    return memberUserId.toString() === userId.toString();
  });

  if (!existingMember) {
    this.members.push({
      user: userId,
      role: role,
      joinedAt: new Date()
    });

    // Update participants array for direct chats
    if (!this.isGroup && this.participants.length < 2) {
      this.participants.push(userId);
    }
  }

  return this.save();
};

chatSchema.methods.removeMember = function(userId) {
  this.members = this.members.filter(member => {
    // Handle both populated and non-populated user references
    const memberUserId = member.user._id || member.user;
    return memberUserId.toString() !== userId.toString();
  });

  // Update participants array for direct chats
  if (!this.isGroup) {
    this.participants = this.participants.filter(id =>
      id.toString() !== userId.toString()
    );
  }

  return this.save();
};

chatSchema.methods.updateLastActivity = function() {
  this.lastActivity = new Date();
  return this.save();
};

chatSchema.methods.isMember = function(userId) {
  return this.members.some(member => {
    // Handle both populated and non-populated user references
    const memberUserId = member.user._id || member.user;
    return memberUserId.toString() === userId.toString();
  });
};

chatSchema.methods.isAdmin = function(userId) {
  const member = this.members.find(member => {
    // Handle both populated and non-populated user references
    const memberUserId = member.user._id || member.user;
    return memberUserId.toString() === userId.toString();
  });
  return member && member.role === 'admin';
};

// Static methods
chatSchema.statics.findDirectChat = function(userId1, userId2) {
  return this.findOne({
    isGroup: false,
    participants: { $all: [userId1, userId2] }
  });
};

chatSchema.statics.findUserChats = function(userId) {
  return this.find({
    'members.user': userId
  })
  .populate('members.user', 'name email avatar isOnline lastSeen')
  .populate('lastMessage')
  .sort({ lastActivity: -1 });
};

chatSchema.statics.createDirectChat = function(userId1, userId2) {
  return this.create({
    isGroup: false,
    members: [
      { user: userId1, role: 'member' },
      { user: userId2, role: 'member' }
    ],
    participants: [userId1, userId2]
  });
};

module.exports = mongoose.model('Chat', chatSchema);
