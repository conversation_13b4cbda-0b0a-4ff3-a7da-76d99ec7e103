import React, { useState, useEffect } from 'react';
import { Search, X, Users } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { usersAPI } from '../../api/users';
import { useChats } from '../../hooks/useChats';
import { Avatar } from '../ui/Avatar';
import { Button } from '../ui/Button';
import { LoadingSpinner } from '../ui/LoadingSpinner';

export const UserSearch = ({ onUserSelect, onClose }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedQuery, setDebouncedQuery] = useState('');
  const { createDirectChat, isCreatingDirectChat } = useChats();

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(searchQuery);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Search users
  const {
    data: searchResults,
    isLoading: isSearching,
    isError,
    error
  } = useQuery({
    queryKey: ['users', 'search', debouncedQuery],
    queryFn: () => usersAPI.searchUsers(debouncedQuery),
    enabled: debouncedQuery.length >= 2,
    staleTime: 30 * 1000, // 30 seconds
  });

  const handleUserClick = async (user) => {
    try {
      console.log('Creating direct chat with user:', user);
      const result = await createDirectChat(user.id);
      console.log('Direct chat created successfully:', result);
      onUserSelect(result.chat);
      onClose(); // Close the search modal after successful chat creation
    } catch (error) {
      console.error('Failed to create chat:', error);
      // Show user-friendly error message
      alert(`Failed to create chat: ${error.message || 'Unknown error'}`);
    }
  };

  const handleCreateGroup = () => {
    // TODO: Implement group creation
    console.log('Create group chat');
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4 max-h-[80vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">New Chat</h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="p-2"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>

        {/* Search input */}
        <div className="p-4 border-b border-gray-200">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search users by name or email..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              autoFocus
            />
          </div>
        </div>

        {/* Quick actions */}
        <div className="p-4 border-b border-gray-200">
          <Button
            variant="secondary"
            onClick={handleCreateGroup}
            className="w-full justify-start"
          >
            <Users className="w-4 h-4 mr-2" />
            Create Group Chat
          </Button>
        </div>

        {/* Search results */}
        <div className="flex-1 overflow-y-auto">
          {!debouncedQuery ? (
            <div className="p-8 text-center text-gray-500">
              <Search className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p>Search for users to start a conversation</p>
              <p className="text-sm mt-1">Type at least 2 characters</p>
            </div>
          ) : isSearching ? (
            <div className="p-8 text-center">
              <LoadingSpinner size="md" className="mx-auto mb-4" />
              <p className="text-gray-500">Searching users...</p>
            </div>
          ) : isError ? (
            <div className="p-8 text-center text-red-500">
              <p>Error searching users</p>
              <p className="text-sm mt-1">{error?.message}</p>
            </div>
          ) : searchResults?.users?.length === 0 ? (
            <div className="p-8 text-center text-gray-500">
              <p>No users found</p>
              <p className="text-sm mt-1">Try a different search term</p>
            </div>
          ) : (
            <div className="divide-y divide-gray-100">
              {searchResults?.users?.map(user => (
                <div
                  key={user.id}
                  onClick={() => handleUserClick(user)}
                  className="flex items-center p-4 hover:bg-gray-50 cursor-pointer transition-colors duration-150"
                >
                  <Avatar
                    src={user.avatar}
                    name={user.name}
                    size="md"
                    isOnline={user.isOnline}
                    className="mr-3"
                  />
                  <div className="flex-1 min-w-0">
                    <h3 className="text-sm font-medium text-gray-900 truncate">
                      {user.name}
                    </h3>
                    <p className="text-sm text-gray-500 truncate">
                      {user.email}
                    </p>
                    {user.isOnline && (
                      <p className="text-xs text-green-600 mt-1">Online</p>
                    )}
                  </div>
                  {isCreatingDirectChat && (
                    <LoadingSpinner size="sm" className="ml-2" />
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200 text-center">
          <p className="text-xs text-gray-500">
            Select a user to start a direct conversation
          </p>
        </div>
      </div>
    </div>
  );
};
