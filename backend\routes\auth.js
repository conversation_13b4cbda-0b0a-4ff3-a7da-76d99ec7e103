const express = require('express');
const { verifyIdToken, getFirebaseUser } = require('../config/firebase');
const User = require('../models/User');
const authMiddleware = require('../middlewares/auth');

const router = express.Router();

/**
 * POST /api/auth/verify
 * Verify Firebase ID token and return user info
 */
router.post('/verify', async (req, res) => {
  try {
    const { idToken } = req.body;
    
    if (!idToken) {
      return res.status(400).json({ 
        error: 'Bad Request', 
        message: 'ID token is required' 
      });
    }
    
    // Verify the Firebase ID token
    const decodedToken = await verifyIdToken(idToken);
    
    // Find or create user in our database using the safe method
    const user = await User.findOrCreateByFirebaseUid(decodedToken.uid, {
      name: decodedToken.name,
      email: decodedToken.email,
      avatar: decodedToken.picture
    });
    
    res.status(200).json({
      success: true,
      message: 'Token verified successfully',
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        avatar: user.avatar,
        isOnline: user.isOnline,
        lastSeen: user.lastSeen,
        preferences: user.preferences
      }
    });
    
  } catch (error) {
    console.error('Token verification error:', error.message);

    if (error.code === 'auth/id-token-expired') {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Token expired'
      });
    }

    if (error.code === 'auth/id-token-revoked') {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Token revoked'
      });
    }

    // Handle MongoDB duplicate key errors
    if (error.code === 11000) {
      console.error('Duplicate key error in token verification:', error.message);
      return res.status(500).json({
        error: 'Internal Server Error',
        message: 'User creation conflict'
      });
    }

    res.status(401).json({
      error: 'Unauthorized',
      message: 'Invalid token'
    });
  }
});

/**
 * GET /api/auth/me
 * Get current user info (requires authentication)
 */
router.get('/me', authMiddleware, async (req, res) => {
  try {
    const user = req.user;
    
    res.status(200).json({
      success: true,
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        avatar: user.avatar,
        isOnline: user.isOnline,
        lastSeen: user.lastSeen,
        preferences: user.preferences,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }
    });
    
  } catch (error) {
    console.error('Get user info error:', error.message);
    res.status(500).json({ 
      error: 'Internal Server Error', 
      message: 'Failed to get user info' 
    });
  }
});

/**
 * POST /api/auth/logout
 * Logout user (set offline status)
 */
router.post('/logout', authMiddleware, async (req, res) => {
  try {
    const user = req.user;
    
    // Set user offline
    await user.setOffline();
    
    res.status(200).json({
      success: true,
      message: 'Logged out successfully'
    });
    
  } catch (error) {
    console.error('Logout error:', error.message);
    res.status(500).json({ 
      error: 'Internal Server Error', 
      message: 'Failed to logout' 
    });
  }
});

/**
 * DELETE /api/auth/account
 * Delete user account (soft delete)
 */
router.delete('/account', authMiddleware, async (req, res) => {
  try {
    const user = req.user;
    
    // In a real app, you might want to:
    // 1. Remove user from all chats
    // 2. Delete or anonymize their messages
    // 3. Delete from Firebase Auth
    // For now, we'll just mark as deleted
    
    await User.findByIdAndDelete(user._id);
    
    res.status(200).json({
      success: true,
      message: 'Account deleted successfully'
    });
    
  } catch (error) {
    console.error('Delete account error:', error.message);
    res.status(500).json({ 
      error: 'Internal Server Error', 
      message: 'Failed to delete account' 
    });
  }
});

module.exports = router;
