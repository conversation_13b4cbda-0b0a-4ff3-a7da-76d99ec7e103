const mongoose = require('mongoose');

const messageSchema = new mongoose.Schema({
  // Message content
  text: {
    type: String,
    required: true,
    trim: true,
    maxlength: 4000
  },
  
  // Message type (text, image, file, etc.)
  type: {
    type: String,
    enum: ['text', 'image', 'file', 'system'],
    default: 'text'
  },
  
  // References
  chatId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Chat',
    required: true,
    index: true
  },
  
  senderId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  
  // Message status tracking
  status: {
    // Users who have received the message
    delivered: [{
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      timestamp: {
        type: Date,
        default: Date.now
      }
    }],
    
    // Users who have seen the message
    seen: [{
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      timestamp: {
        type: Date,
        default: Date.now
      }
    }]
  },
  
  // File attachments (for future use)
  attachments: [{
    type: {
      type: String,
      enum: ['image', 'file', 'audio', 'video']
    },
    url: String,
    filename: String,
    size: Number,
    mimeType: String
  }],
  
  // Reply to another message (for future use)
  replyTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Message',
    default: null
  },
  
  // Message reactions (for future use)
  reactions: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    emoji: String,
    timestamp: {
      type: Date,
      default: Date.now
    }
  }],
  
  // Soft delete
  isDeleted: {
    type: Boolean,
    default: false
  },
  
  deletedAt: {
    type: Date,
    default: null
  },
  
  // Edit tracking
  isEdited: {
    type: Boolean,
    default: false
  },
  
  editedAt: {
    type: Date,
    default: null
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      delete ret.__v;
      return ret;
    }
  }
});

// Indexes for better performance
messageSchema.index({ chatId: 1, createdAt: -1 });
messageSchema.index({ senderId: 1 });
messageSchema.index({ 'status.delivered.user': 1 });
messageSchema.index({ 'status.seen.user': 1 });
messageSchema.index({ isDeleted: 1 });

// Compound index for pagination
messageSchema.index({ chatId: 1, _id: -1 });

// Instance methods
messageSchema.methods.markAsDelivered = function(userId) {
  const alreadyDelivered = this.status.delivered.some(delivery => 
    delivery.user.toString() === userId.toString()
  );
  
  if (!alreadyDelivered) {
    this.status.delivered.push({
      user: userId,
      timestamp: new Date()
    });
  }
  
  return this.save();
};

messageSchema.methods.markAsSeen = function(userId) {
  const alreadySeen = this.status.seen.some(seen =>
    seen.user.toString() === userId.toString()
  );

  if (!alreadySeen) {
    this.status.seen.push({
      user: userId,
      timestamp: new Date()
    });

    // Also mark as delivered if not already (without calling save)
    const alreadyDelivered = this.status.delivered.some(delivery =>
      delivery.user.toString() === userId.toString()
    );

    if (!alreadyDelivered) {
      this.status.delivered.push({
        user: userId,
        timestamp: new Date()
      });
    }
  }

  return this.save();
};

messageSchema.methods.isDeliveredTo = function(userId) {
  return this.status.delivered.some(delivery => 
    delivery.user.toString() === userId.toString()
  );
};

messageSchema.methods.isSeenBy = function(userId) {
  return this.status.seen.some(seen => 
    seen.user.toString() === userId.toString()
  );
};

messageSchema.methods.softDelete = function() {
  this.isDeleted = true;
  this.deletedAt = new Date();
  return this.save();
};

messageSchema.methods.edit = function(newText) {
  this.text = newText;
  this.isEdited = true;
  this.editedAt = new Date();
  return this.save();
};

// Static methods
messageSchema.statics.getChatMessages = function(chatId, limit = 50, before = null) {
  const query = { 
    chatId, 
    isDeleted: false 
  };
  
  if (before) {
    query._id = { $lt: before };
  }
  
  return this.find(query)
    .populate('senderId', 'name avatar')
    .sort({ _id: -1 })
    .limit(limit);
};

messageSchema.statics.markChatMessagesAsDelivered = function(chatId, userId, excludeSender = true) {
  const query = { 
    chatId,
    isDeleted: false,
    'status.delivered.user': { $ne: userId }
  };
  
  if (excludeSender) {
    query.senderId = { $ne: userId };
  }
  
  return this.updateMany(query, {
    $push: {
      'status.delivered': {
        user: userId,
        timestamp: new Date()
      }
    }
  });
};

messageSchema.statics.markChatMessagesAsSeen = function(chatId, userId, excludeSender = true) {
  const query = { 
    chatId,
    isDeleted: false,
    'status.seen.user': { $ne: userId }
  };
  
  if (excludeSender) {
    query.senderId = { $ne: userId };
  }
  
  return this.updateMany(query, {
    $push: {
      'status.seen': {
        user: userId,
        timestamp: new Date()
      },
      'status.delivered': {
        user: userId,
        timestamp: new Date()
      }
    }
  });
};

module.exports = mongoose.model('Message', messageSchema);
