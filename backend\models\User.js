const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
  // Firebase UID as the primary identifier
  firebaseUid: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  
  // User profile information
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  
  avatar: {
    type: String,
    default: null // URL to profile picture
  },
  
  // Online status tracking
  isOnline: {
    type: Boolean,
    default: false
  },
  
  lastSeen: {
    type: Date,
    default: Date.now
  },
  
  // Socket connection tracking
  socketId: {
    type: String,
    default: null
  },
  
  // User preferences
  preferences: {
    notifications: {
      type: Boolean,
      default: true
    },
    soundEnabled: {
      type: Boolean,
      default: true
    }
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      delete ret.firebaseUid;
      delete ret.socketId;
      delete ret.__v;
      return ret;
    }
  }
});

// Indexes for better performance
userSchema.index({ email: 1 });
userSchema.index({ isOnline: 1 });
userSchema.index({ lastSeen: -1 });

// Instance methods
userSchema.methods.setOnline = function(socketId = null) {
  this.isOnline = true;
  this.lastSeen = new Date();
  this.socketId = socketId;
  return this.save();
};

userSchema.methods.setOffline = function() {
  this.isOnline = false;
  this.lastSeen = new Date();
  this.socketId = null;
  return this.save();
};

// Static methods
userSchema.statics.findByFirebaseUid = function(firebaseUid) {
  return this.findOne({ firebaseUid });
};

userSchema.statics.findOnlineUsers = function() {
  return this.find({ isOnline: true }).select('_id name avatar isOnline lastSeen');
};

userSchema.statics.searchUsers = function(query, excludeIds = []) {
  const searchRegex = new RegExp(query, 'i');
  return this.find({
    $and: [
      {
        $or: [
          { name: searchRegex },
          { email: searchRegex }
        ]
      },
      { _id: { $nin: excludeIds } }
    ]
  }).select('_id name email avatar isOnline lastSeen').limit(20);
};

// Safe user creation method that handles race conditions
userSchema.statics.findOrCreateByFirebaseUid = async function(firebaseUid, userData) {
  try {
    // First try to find existing user
    let user = await this.findOne({ firebaseUid });
    if (user) {
      return user;
    }

    // Try to create new user
    try {
      user = new this({
        firebaseUid,
        name: userData.name || userData.email?.split('@')[0] || 'User',
        email: userData.email,
        avatar: userData.avatar || null
      });

      await user.save();
      console.log(`New user created: ${user.email}`);
      return user;
    } catch (saveError) {
      // Handle duplicate key error (race condition)
      if (saveError.code === 11000) {
        // Another request created the user, try to find it
        user = await this.findOne({ firebaseUid });
        if (!user) {
          // If still not found by firebaseUid, try by email
          user = await this.findOne({ email: userData.email });
        }
        if (!user) {
          throw new Error('Failed to create or find user after duplicate key error');
        }
        console.log(`Found existing user after duplicate key error: ${user.email}`);
        return user;
      } else {
        throw saveError;
      }
    }
  } catch (error) {
    console.error('Error in findOrCreateByFirebaseUid:', error.message);
    throw error;
  }
};

module.exports = mongoose.model('User', userSchema);
