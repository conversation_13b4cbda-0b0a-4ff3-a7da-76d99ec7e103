const mongoose = require('mongoose');
require('dotenv').config();

const User = require('./models/User');
const Chat = require('./models/Chat');
const Message = require('./models/Message');

async function simpleTest() {
  try {
    console.log('🔌 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Get existing users
    const users = await User.find().limit(2);
    console.log(`✅ Found ${users.length} users`);

    if (users.length < 2) {
      console.log('❌ Need at least 2 users for testing');
      return;
    }

    const user1 = users[0];
    const user2 = users[1];

    // Find or create a chat
    let chat = await Chat.findDirectChat(user1._id, user2._id);
    if (!chat) {
      chat = await Chat.createDirectChat(user1._id, user2._id);
      console.log('✅ Created new chat');
    } else {
      console.log('✅ Found existing chat');
    }

    console.log(`\n📝 Testing message store and fetch:`);
    console.log(`Chat: ${chat._id}`);
    console.log(`User1: ${user1.name} (${user1._id})`);
    console.log(`User2: ${user2.name} (${user2._id})`);

    // 1. Store a message
    const messageText = `Hello from ${user1.name} at ${new Date().toLocaleTimeString()}`;
    const message = new Message({
      text: messageText,
      type: 'text',
      chatId: chat._id,
      senderId: user1._id
    });

    await message.save();
    console.log(`✅ Message stored: ${message._id}`);
    console.log(`   Text: "${message.text}"`);

    // 2. Populate sender info
    await message.populate('senderId', 'name avatar');
    console.log(`✅ Sender populated: ${message.senderId.name}`);

    // 3. Fetch the message back
    const fetchedMessage = await Message.findById(message._id)
      .populate('senderId', 'name avatar');
    
    console.log(`✅ Message fetched successfully:`);
    console.log(`   ID: ${fetchedMessage._id}`);
    console.log(`   Text: "${fetchedMessage.text}"`);
    console.log(`   Sender: ${fetchedMessage.senderId.name}`);
    console.log(`   Created: ${fetchedMessage.createdAt}`);

    // 4. Fetch all messages in chat
    const chatMessages = await Message.getChatMessages(chat._id, 10);
    console.log(`✅ Found ${chatMessages.length} messages in chat`);

    // 5. Test message status updates
    await message.markAsDelivered(user2._id);
    console.log(`✅ Message marked as delivered`);

    await message.markAsSeen(user2._id);
    console.log(`✅ Message marked as seen`);

    // 6. Verify status
    const updatedMessage = await Message.findById(message._id);
    console.log(`✅ Final status:`);
    console.log(`   Delivered: ${updatedMessage.status.delivered.length} users`);
    console.log(`   Seen: ${updatedMessage.status.seen.length} users`);

    console.log(`\n🎉 All database operations working perfectly!`);

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await mongoose.connection.close();
    console.log('📦 Database connection closed');
  }
}

simpleTest();
