const { verifyIdToken } = require('../config/firebase');
const User = require('../models/User');

/**
 * Middleware to authenticate requests using Firebase ID tokens
 * Expects Authorization header with format: "Bearer <idToken>"
 */
const authMiddleware = async (req, res, next) => {
  try {
    // Extract token from Authorization header
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ 
        error: 'Unauthorized', 
        message: 'No valid authorization token provided' 
      });
    }
    
    const idToken = authHeader.split('Bearer ')[1];
    
    if (!idToken) {
      return res.status(401).json({ 
        error: 'Unauthorized', 
        message: 'No token provided' 
      });
    }
    
    // Verify the Firebase ID token
    const decodedToken = await verifyIdToken(idToken);
    
    if (!decodedToken || !decodedToken.uid) {
      return res.status(401).json({ 
        error: 'Unauthorized', 
        message: 'Invalid token' 
      });
    }
    
    // Find or create user in our database using the safe method
    const user = await User.findOrCreateByFirebaseUid(decodedToken.uid, {
      name: decodedToken.name,
      email: decodedToken.email,
      avatar: decodedToken.picture
    });

    // Update user info if changed
    let hasChanges = false;

    if (decodedToken.name && user.name !== decodedToken.name) {
      user.name = decodedToken.name;
      hasChanges = true;
    }

    if (decodedToken.email && user.email !== decodedToken.email) {
      user.email = decodedToken.email;
      hasChanges = true;
    }

    if (decodedToken.picture && user.avatar !== decodedToken.picture) {
      user.avatar = decodedToken.picture;
      hasChanges = true;
    }

    if (hasChanges) {
      await user.save();
    }
    
    // Attach user info to request object
    req.user = user;
    req.firebaseUser = decodedToken;
    
    next();
    
  } catch (error) {
    console.error('Auth middleware error:', error.message);

    // Handle specific Firebase errors
    if (error.code === 'auth/id-token-expired') {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Token expired'
      });
    }

    if (error.code === 'auth/id-token-revoked') {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Token revoked'
      });
    }

    // Handle MongoDB duplicate key errors
    if (error.code === 11000) {
      console.error('Duplicate key error in auth middleware:', error.message);
      return res.status(500).json({
        error: 'Internal Server Error',
        message: 'User creation conflict'
      });
    }

    return res.status(401).json({
      error: 'Unauthorized',
      message: 'Authentication failed'
    });
  }
};

module.exports = authMiddleware;
