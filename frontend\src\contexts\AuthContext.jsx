import React, { createContext, useContext, useEffect, useState } from 'react';
import { 
  onAuthStateChanged, 
  signInWithPopup, 
  signOut as firebaseSignOut,
  getIdToken 
} from 'firebase/auth';
import { auth, googleProvider } from '../config/firebase';
import { authAPI } from '../api/auth';

const AuthContext = createContext({});

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [firebaseUser, setFirebaseUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Sign in with Google
  const signInWithGoogle = async () => {
    try {
      setError(null);
      setLoading(true);
      
      const result = await signInWithPopup(auth, googleProvider);
      const idToken = await getIdToken(result.user);
      
      // Verify token with backend
      const response = await authAPI.verifyToken(idToken);
      
      if (response.success) {
        setUser(response.user);
        localStorage.setItem('idToken', idToken);
        return response.user;
      } else {
        throw new Error('Failed to verify token with backend');
      }
    } catch (error) {
      console.error('Sign in error:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Sign out
  const signOut = async () => {
    try {
      setError(null);
      
      // Call backend logout endpoint
      try {
        await authAPI.logout();
      } catch (backendError) {
        console.warn('Backend logout failed:', backendError);
      }
      
      // Sign out from Firebase
      await firebaseSignOut(auth);
      
      // Clear local state
      setUser(null);
      setFirebaseUser(null);
      localStorage.removeItem('idToken');
      
    } catch (error) {
      console.error('Sign out error:', error);
      setError(error.message);
      throw error;
    }
  };

  // Get current ID token with smart caching
  const getToken = async (forceRefresh = false) => {
    if (!firebaseUser) return null;

    try {
      // Check if we have a cached token that's still valid (unless force refresh)
      if (!forceRefresh) {
        const cachedToken = localStorage.getItem('idToken');
        if (cachedToken) {
          // Check if token is still valid (not expired)
          try {
            const payload = JSON.parse(atob(cachedToken.split('.')[1]));
            const now = Date.now() / 1000;
            // If token expires in more than 5 minutes, use cached version
            if (payload.exp && payload.exp > now + 300) {
              return cachedToken;
            }
          } catch (e) {
            // Invalid token format, get new one
          }
        }
      }

      const token = await getIdToken(firebaseUser, forceRefresh);
      localStorage.setItem('idToken', token);
      return token;
    } catch (error) {
      console.error('Get token error:', error);

      // If quota exceeded, try to use cached token
      if (error.code === 'auth/quota-exceeded') {
        const cachedToken = localStorage.getItem('idToken');
        if (cachedToken) {
          console.warn('Using cached token due to quota exceeded');
          return cachedToken;
        }
      }

      return null;
    }
  };

  // Refresh user data from backend
  const refreshUser = async () => {
    try {
      const response = await authAPI.getCurrentUser();
      if (response.success) {
        setUser(response.user);
        return response.user;
      }
    } catch (error) {
      console.error('Refresh user error:', error);
    }
    return null;
  };

  // Listen for Firebase auth state changes
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      try {
        setFirebaseUser(firebaseUser);
        
        if (firebaseUser) {
          // User is signed in
          const idToken = await getIdToken(firebaseUser);
          localStorage.setItem('idToken', idToken);
          
          // Verify with backend and get user data
          try {
            const response = await authAPI.verifyToken(idToken);
            if (response.success) {
              setUser(response.user);
            } else {
              throw new Error('Backend verification failed');
            }
          } catch (backendError) {
            console.error('Backend verification error:', backendError);
            // If backend verification fails, sign out
            await firebaseSignOut(auth);
            setUser(null);
            localStorage.removeItem('idToken');
          }
        } else {
          // User is signed out
          setUser(null);
          localStorage.removeItem('idToken');
        }
      } catch (error) {
        console.error('Auth state change error:', error);
        setError(error.message);
        setUser(null);
        setFirebaseUser(null);
        localStorage.removeItem('idToken');
      } finally {
        setLoading(false);
      }
    });

    return unsubscribe;
  }, []);

  // Auto-refresh token every 50 minutes with error handling
  useEffect(() => {
    if (!firebaseUser) return;

    const interval = setInterval(async () => {
      try {
        // Only force refresh if we don't have quota issues
        const lastError = localStorage.getItem('lastTokenError');
        const lastErrorTime = localStorage.getItem('lastTokenErrorTime');
        const now = Date.now();

        // If we had a quota error in the last hour, skip refresh
        if (lastError === 'auth/quota-exceeded' && lastErrorTime && (now - parseInt(lastErrorTime)) < 3600000) {
          console.log('Skipping token refresh due to recent quota error');
          return;
        }

        await getToken(true);
        // Clear error flags on successful refresh
        localStorage.removeItem('lastTokenError');
        localStorage.removeItem('lastTokenErrorTime');
      } catch (error) {
        console.error('Token refresh error:', error);
        // Store error info for backoff logic
        localStorage.setItem('lastTokenError', error.code || 'unknown');
        localStorage.setItem('lastTokenErrorTime', now.toString());
      }
    }, 50 * 60 * 1000); // 50 minutes

    return () => clearInterval(interval);
  }, [firebaseUser]);

  const value = {
    user,
    firebaseUser,
    loading,
    error,
    signInWithGoogle,
    signOut,
    getToken,
    refreshUser,
    isAuthenticated: !!user && !!firebaseUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
