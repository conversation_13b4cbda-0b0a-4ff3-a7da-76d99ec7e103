const mongoose = require('mongoose');
require('dotenv').config();

const User = require('./models/User');
const Chat = require('./models/Chat');
const Message = require('./models/Message');

async function testMessaging() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB');

    // Test 1: Check if we can find users
    const users = await User.find().limit(5);
    console.log(`✅ Found ${users.length} users in database`);
    
    if (users.length === 0) {
      console.log('❌ No users found. Please create some users first.');
      return;
    }

    // Test 2: Check if we can find chats
    const chats = await Chat.find().limit(5);
    console.log(`✅ Found ${chats.length} chats in database`);
    
    if (chats.length === 0) {
      console.log('❌ No chats found. Please create some chats first.');
      return;
    }

    // Test 3: Create a test chat with proper membership
    const firstUser = users[0];
    const secondUser = users[1] || users[0]; // Use same user if only one exists

    console.log(`\n🔍 Creating test chat with proper membership:`);
    console.log(`User 1 ID: ${firstUser._id} (${firstUser.name})`);
    console.log(`User 2 ID: ${secondUser._id} (${secondUser.name})`);

    // Create a direct chat between two users
    const testChat = await Chat.createDirectChat(firstUser._id, secondUser._id);
    console.log(`✅ Test chat created with ID: ${testChat._id}`);
    console.log(`Chat members:`, testChat.members.map(m => m.user));
    console.log(`Chat participants:`, testChat.participants);
    console.log(`Is user 1 member: ${testChat.isMember(firstUser._id)}`);
    console.log(`Is user 2 member: ${testChat.isMember(secondUser._id)}`);

    // Test 4: Try to create a message
    console.log(`\n📝 Testing message creation:`);

    const testMessage = new Message({
      text: 'Test message from debug script',
      type: 'text',
      chatId: testChat._id,
      senderId: firstUser._id
    });

    await testMessage.save();
    console.log(`✅ Message created with ID: ${testMessage._id}`);

    // Test 5: Populate sender info
    await testMessage.populate('senderId', 'name avatar');
    console.log(`✅ Message populated:`, {
      id: testMessage._id,
      text: testMessage.text,
      sender: testMessage.senderId.name,
      chatId: testMessage.chatId
    });

    // Test 6: Update chat last message
    testChat.lastMessage = testMessage._id;
    await testChat.updateLastActivity();
    console.log(`✅ Chat last message updated`);

    // Test 7: Test message delivery marking
    if (users.length > 1) {
      await testMessage.markAsDelivered(secondUser._id);
      console.log(`✅ Message marked as delivered to user ${secondUser._id}`);
    }

    // Test 8: Test bulk message operations
    const messageCount = await Message.countDocuments({ chatId: testChat._id });
    console.log(`✅ Found ${messageCount} messages in chat ${testChat._id}`);

    // Test 9: Test socket-like message sending flow
    console.log(`\n🔌 Testing socket-like message flow:`);

    // Simulate the exact flow from socket handler
    const socketTestMessage = new Message({
      text: 'Socket test message',
      type: 'text',
      chatId: testChat._id,
      senderId: firstUser._id
    });

    await socketTestMessage.save();
    await socketTestMessage.populate('senderId', 'name avatar');

    // Update chat's last message and activity
    testChat.lastMessage = socketTestMessage._id;
    await testChat.updateLastActivity();

    console.log(`✅ Socket-style message created and saved:`, {
      id: socketTestMessage._id,
      text: socketTestMessage.text,
      sender: socketTestMessage.senderId.name,
      chatId: socketTestMessage.chatId
    });

    // Clean up test messages and chat
    await Message.findByIdAndDelete(testMessage._id);
    await Message.findByIdAndDelete(socketTestMessage._id);
    await Chat.findByIdAndDelete(testChat._id);
    console.log(`✅ Test data cleaned up`);

    console.log('\n🎉 All tests passed! Messaging system appears to be working correctly.');

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    await mongoose.connection.close();
    console.log('📦 Database connection closed');
  }
}

// Run the test
testMessaging();
