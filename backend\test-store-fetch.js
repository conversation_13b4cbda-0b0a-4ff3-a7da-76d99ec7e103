const mongoose = require('mongoose');
require('dotenv').config();

const User = require('./models/User');
const Chat = require('./models/Chat');
const Message = require('./models/Message');

async function testStoreAndFetch() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB');

    // Get existing users
    const users = await User.find().limit(2);
    if (users.length < 2) {
      console.log('❌ Need at least 2 users. Creating test users...');
      
      const user1 = new User({
        name: 'Test User 1',
        email: '<EMAIL>',
        firebaseUid: 'test-uid-1',
        avatar: null
      });
      
      const user2 = new User({
        name: 'Test User 2', 
        email: '<EMAIL>',
        firebaseUid: 'test-uid-2',
        avatar: null
      });
      
      await user1.save();
      await user2.save();
      users.push(user1, user2);
      console.log('✅ Test users created');
    }

    const user1 = users[0];
    const user2 = users[1];

    // Create or find a chat between the users
    let chat = await Chat.findDirectChat(user1._id, user2._id);
    if (!chat) {
      chat = await Chat.createDirectChat(user1._id, user2._id);
      console.log('✅ New chat created between users');
    } else {
      console.log('✅ Found existing chat between users');
    }

    console.log(`\n📝 STEP 1: Storing message in database`);
    console.log(`Chat ID: ${chat._id}`);
    console.log(`Sender: ${user1.name} (${user1._id})`);
    console.log(`Receiver: ${user2.name} (${user2._id})`);

    // Store a new message
    const messageText = `Test message stored at ${new Date().toISOString()}`;
    const newMessage = new Message({
      text: messageText,
      type: 'text',
      chatId: chat._id,
      senderId: user1._id
    });

    await newMessage.save();
    console.log(`✅ Message stored with ID: ${newMessage._id}`);
    console.log(`Message text: "${newMessage.text}"`);
    console.log(`Created at: ${newMessage.createdAt}`);

    // Populate sender information
    await newMessage.populate('senderId', 'name email avatar');
    console.log(`✅ Message populated with sender info: ${newMessage.senderId.name}`);

    // Update chat's last message
    chat.lastMessage = newMessage._id;
    await chat.updateLastActivity();
    console.log(`✅ Chat updated with last message`);

    console.log(`\n📖 STEP 2: Fetching messages from database`);

    // Method 1: Fetch by message ID
    const fetchedById = await Message.findById(newMessage._id)
      .populate('senderId', 'name email avatar');
    
    console.log(`✅ Fetched by ID:`, {
      id: fetchedById._id,
      text: fetchedById.text,
      sender: fetchedById.senderId.name,
      createdAt: fetchedById.createdAt
    });

    // Method 2: Fetch all messages in the chat
    const chatMessages = await Message.getChatMessages(chat._id, 10);
    console.log(`✅ Fetched ${chatMessages.length} messages from chat`);
    
    chatMessages.forEach((msg, index) => {
      console.log(`  ${index + 1}. [${msg.createdAt}] ${msg.senderId.name}: ${msg.text}`);
    });

    // Method 3: Fetch using the route logic (simulate API call)
    const routeMessages = await Message.find({ 
      chatId: chat._id, 
      isDeleted: false 
    })
    .populate('senderId', 'name avatar')
    .sort({ _id: -1 })
    .limit(50);

    console.log(`✅ Fetched ${routeMessages.length} messages using route logic`);

    // Method 4: Test pagination
    console.log(`\n📄 STEP 3: Testing pagination`);
    
    // Add a few more messages for pagination test
    for (let i = 1; i <= 3; i++) {
      const msg = new Message({
        text: `Pagination test message ${i}`,
        type: 'text',
        chatId: chat._id,
        senderId: i % 2 === 0 ? user1._id : user2._id
      });
      await msg.save();
      console.log(`✅ Added pagination test message ${i}`);
    }

    // Fetch with pagination
    const paginatedMessages = await Message.getChatMessages(chat._id, 2);
    console.log(`✅ Fetched ${paginatedMessages.length} messages with limit 2`);
    
    if (paginatedMessages.length > 0) {
      const beforeId = paginatedMessages[paginatedMessages.length - 1]._id;
      const nextPage = await Message.getChatMessages(chat._id, 2, beforeId);
      console.log(`✅ Fetched ${nextPage.length} messages for next page`);
    }

    console.log(`\n🔍 STEP 4: Verifying message delivery status`);
    
    // Mark message as delivered
    await newMessage.markAsDelivered(user2._id);
    const deliveredMessage = await Message.findById(newMessage._id);
    console.log(`✅ Message delivery status:`, deliveredMessage.status.delivered);

    // Mark message as seen
    await newMessage.markAsSeen(user2._id);
    const seenMessage = await Message.findById(newMessage._id);
    console.log(`✅ Message seen status:`, seenMessage.status.seen);

    console.log(`\n🎉 All store and fetch operations completed successfully!`);
    
    // Show final summary
    const totalMessages = await Message.countDocuments({ chatId: chat._id });
    console.log(`📊 Total messages in chat: ${totalMessages}`);

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    await mongoose.connection.close();
    console.log('📦 Database connection closed');
  }
}

// Run the test
testStoreAndFetch();
