import axios from 'axios';

// Create axios instance
const apiClient = axios.create({
  baseURL: (import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000') + '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  async (config) => {
    const token = localStorage.getItem('idToken');
    if (token) {
      // Check if token is about to expire (within 5 minutes)
      try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        const expirationTime = payload.exp * 1000; // Convert to milliseconds
        const currentTime = Date.now();
        const timeUntilExpiry = expirationTime - currentTime;

        if (timeUntilExpiry < 5 * 60 * 1000) { // Less than 5 minutes
          console.warn('Token expiring soon, should refresh');
          // Note: We can't refresh here directly as we don't have access to Firebase auth
          // The AuthContext should handle this
        }
      } catch (e) {
        console.warn('Could not parse token expiration:', e);
      }

      config.headers.Authorization = `Bearer ${token}`;
      console.log('API Request:', config.method?.toUpperCase(), config.url, 'with token');
    } else {
      console.warn('API Request:', config.method?.toUpperCase(), config.url, 'WITHOUT token');
    }
    return config;
  },
  (error) => {
    console.error('Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
apiClient.interceptors.response.use(
  (response) => {
    console.log('API Response:', response.config.method?.toUpperCase(), response.config.url, response.status);
    return response.data;
  },
  (error) => {
    console.error('API Error:', error);

    // Handle network errors
    if (!error.response) {
      console.error('Network error:', error.message);
      throw new Error('Network error. Please check your connection.');
    }

    // Handle HTTP errors
    const { status, data } = error.response;
    console.error('HTTP Error:', status, data);
    
    switch (status) {
      case 401:
        // Unauthorized - token expired or invalid
        localStorage.removeItem('idToken');
        window.location.href = '/login';
        throw new Error(data?.message || 'Authentication failed');
        
      case 403:
        // Forbidden
        throw new Error(data?.message || 'Access denied');
        
      case 404:
        // Not found
        throw new Error(data?.message || 'Resource not found');
        
      case 429:
        // Rate limited
        throw new Error(data?.message || 'Too many requests. Please try again later.');
        
      case 500:
        // Server error
        throw new Error(data?.message || 'Server error. Please try again later.');
        
      default:
        throw new Error(data?.message || `HTTP ${status} error`);
    }
  }
);

export default apiClient;
