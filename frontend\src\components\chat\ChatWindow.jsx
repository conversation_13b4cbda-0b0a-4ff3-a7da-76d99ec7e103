import React, { useEffect, useRef } from 'react';
import { useChat } from '../../hooks/useChats';
import { useInfiniteMessages } from '../../hooks/useInfiniteMessages';
import { useSocket } from '../../contexts/SocketContext';
import { ChatHeader } from './ChatHeader';
import { MessageList } from './MessageList';
import { MessageInput } from './MessageInput';
import { LoadingSpinner } from '../ui/LoadingSpinner';

export const ChatWindow = ({ chatId, onBack }) => {
  const { data: chat, isLoading: chatLoading } = useChat(chatId);
  const {
    messages,
    isLoading: messagesLoading,
    hasNextPage,
    isFetchingNextPage,
    loadMoreMessages,
    addOptimisticMessage,
    updateMessage,
    addRealtimeMessage,
    updateMessageStatus
  } = useInfiniteMessages(chatId);
  
  const socket = useSocket();
  const messageListRef = useRef(null);

  // Join chat room when component mounts
  useEffect(() => {
    if (chatId && socket.isConnected) {
      socket.joinChat(chatId);
      
      return () => {
        socket.leaveChat(chatId);
      };
    }
  }, [chatId, socket]);

  // Handle real-time message events
  useEffect(() => {
    if (!socket.isConnected) return;

    const handleNewMessage = (data) => {
      if (data.message.chatId === chatId) {
        // Check if this is a response to our optimistic message
        if (data.tempId) {
          updateMessage(data.tempId, data.message);
        } else {
          addRealtimeMessage(data.message);
        }
        
        // Scroll to bottom for new messages
        if (messageListRef.current) {
          messageListRef.current.scrollToBottom();
        }
      }
    };

    const handleMessageDelivered = (data) => {
      if (data.messageId) {
        updateMessageStatus(data.messageId, {
          delivered: [...(messages.find(m => m._id === data.messageId)?.status?.delivered || []), 
                     ...data.deliveredTo.map(userId => ({ user: userId, timestamp: new Date() }))]
        });
      }
    };

    const handleMessageSeen = (data) => {
      if (data.messageIds === 'all' || Array.isArray(data.messageIds)) {
        // Update seen status for messages
        // This is a simplified implementation
        console.log('Messages seen:', data);
      }
    };

    socket.addEventListener('message:new', handleNewMessage);
    socket.addEventListener('message:delivered', handleMessageDelivered);
    socket.addEventListener('message:seen', handleMessageSeen);

    return () => {
      socket.removeEventListener('message:new', handleNewMessage);
      socket.removeEventListener('message:delivered', handleMessageDelivered);
      socket.removeEventListener('message:seen', handleMessageSeen);
    };
  }, [socket, chatId, messages, addRealtimeMessage, updateMessage, updateMessageStatus]);

  // Mark messages as seen when chat is opened
  useEffect(() => {
    if (chatId && socket.isConnected && messages.length > 0) {
      // Mark all messages as seen
      socket.markMessagesSeen(chatId);
    }
  }, [chatId, socket, messages.length]);

  const handleSendMessage = (text) => {
    if (!text.trim() || !socket.isConnected) return;

    // Generate temporary ID for optimistic update
    const tempId = `temp_${Date.now()}_${Math.random()}`;
    
    // Create optimistic message
    const optimisticMessage = {
      _id: tempId,
      tempId,
      text: text.trim(),
      type: 'text',
      chatId,
      senderId: {
        _id: 'current_user', // This should be the current user's ID
        name: 'You',
        avatar: null
      },
      createdAt: new Date().toISOString(),
      status: {
        delivered: [],
        seen: []
      },
      isOptimistic: true
    };

    // Add optimistic message to UI
    addOptimisticMessage(optimisticMessage);

    // Send message via socket
    socket.sendMessage(chatId, text.trim(), tempId);

    // Scroll to bottom
    if (messageListRef.current) {
      setTimeout(() => {
        messageListRef.current.scrollToBottom();
      }, 50);
    }
  };

  if (chatLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!chat) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center text-gray-500">
          <p>Chat not found</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col h-full">
      {/* Chat header */}
      <ChatHeader 
        chat={chat} 
        onBack={onBack}
        socket={socket}
      />

      {/* Messages */}
      <div className="flex-1 flex flex-col min-h-0">
        <MessageList
          ref={messageListRef}
          messages={messages}
          isLoading={messagesLoading}
          hasNextPage={hasNextPage}
          isFetchingNextPage={isFetchingNextPage}
          onLoadMore={loadMoreMessages}
          chatId={chatId}
          socket={socket}
        />
      </div>

      {/* Message input */}
      <MessageInput
        onSendMessage={handleSendMessage}
        disabled={!socket.isConnected}
        chatId={chatId}
        socket={socket}
      />
    </div>
  );
};
