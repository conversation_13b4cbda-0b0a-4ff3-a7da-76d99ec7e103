import React, { useEffect, useRef } from 'react';
import { useChat } from '../../hooks/useChats';
import { useInfiniteMessages } from '../../hooks/useInfiniteMessages';
import { useSocket } from '../../contexts/SocketContext';
import { ChatHeader } from './ChatHeader';
import { MessageList } from './MessageList';
import { MessageInput } from './MessageInput';
import { LoadingSpinner } from '../ui/LoadingSpinner';

export const ChatWindow = ({ chatId, onBack }) => {
  const { data: chat, isLoading: chatLoading } = useChat(chatId);
  const {
    messages,
    isLoading: messagesLoading,
    hasNextPage,
    isFetchingNextPage,
    loadMoreMessages,
    addOptimisticMessage,
    updateMessage,
    addRealtimeMessage,
    updateMessageStatus
  } = useInfiniteMessages(chatId);
  
  const socket = useSocket();
  const messageListRef = useRef(null);

  // Join chat room when component mounts
  useEffect(() => {
    if (chatId && socket.isConnected) {
      socket.joinChat(chatId);
      
      return () => {
        socket.leaveChat(chatId);
      };
    }
  }, [chatId, socket]);

  // Handle real-time message events
  useEffect(() => {
    if (!socket.isConnected) return;

    const handleNewMessage = (data) => {
      console.log('Received new message:', data);
      if (data.message.chatId === chatId) {
        // Check if this is a response to our optimistic message
        if (data.tempId) {
          console.log('Updating optimistic message:', data.tempId);
          updateMessage(data.tempId, data.message);
        } else {
          console.log('Adding realtime message:', data.message._id);
          addRealtimeMessage(data.message);
        }

        // Scroll to bottom for new messages
        if (messageListRef.current) {
          messageListRef.current.scrollToBottom();
        }
      }
    };

    const handleMessageDelivered = (data) => {
      console.log('Message delivered:', data);
      if (data.messageId) {
        updateMessageStatus(data.messageId, {
          delivered: [...(messages.find(m => m._id === data.messageId)?.status?.delivered || []),
                     ...data.deliveredTo.map(userId => ({ user: userId, timestamp: new Date() }))]
        });
      }
    };

    const handleMessageSeen = (data) => {
      console.log('Messages seen:', data);
      if (data.messageIds === 'all' || Array.isArray(data.messageIds)) {
        // Update seen status for messages
        // This is a simplified implementation
      }
    };

    const handleMessageError = (error) => {
      console.error('Message error received:', error);

      // If there's a tempId, we need to mark the optimistic message as failed
      if (error.tempId) {
        updateMessage(error.tempId, {
          error: true,
          errorMessage: error.message,
          errorCode: error.code
        });
      }

      // You can add toast notification here
      // toast.error(error.message);
    };

    const handleMessageSent = (data) => {
      console.log('Message sent confirmation:', data);
      // Message was successfully sent and saved to database
    };

    socket.addEventListener('message:new', handleNewMessage);
    socket.addEventListener('message:delivered', handleMessageDelivered);
    socket.addEventListener('message:seen', handleMessageSeen);
    socket.addEventListener('message:error', handleMessageError);
    socket.addEventListener('message:sent', handleMessageSent);

    return () => {
      socket.removeEventListener('message:new', handleNewMessage);
      socket.removeEventListener('message:delivered', handleMessageDelivered);
      socket.removeEventListener('message:seen', handleMessageSeen);
      socket.removeEventListener('message:error', handleMessageError);
      socket.removeEventListener('message:sent', handleMessageSent);
    };
  }, [socket, chatId, messages, addRealtimeMessage, updateMessage, updateMessageStatus]);

  // Mark messages as seen when chat is opened
  useEffect(() => {
    if (chatId && socket.isConnected && messages.length > 0) {
      // Mark all messages as seen
      socket.markMessagesSeen(chatId);
    }
  }, [chatId, socket, messages.length]);

  const handleSendMessage = (text) => {
    if (!text.trim()) {
      console.warn('Cannot send empty message');
      return;
    }

    if (!socket.isConnected) {
      console.warn('Cannot send message: socket not connected');
      // You can add toast notification here
      // toast.error('Not connected. Please wait for connection to be restored.');
      return;
    }

    if (!chatId) {
      console.error('Cannot send message: no chat ID');
      return;
    }

    console.log('Sending message:', { chatId, text: text.substring(0, 50) });

    // Generate temporary ID for optimistic update
    const tempId = `temp_${Date.now()}_${Math.random()}`;

    // Create optimistic message
    const optimisticMessage = {
      _id: tempId,
      tempId,
      text: text.trim(),
      type: 'text',
      chatId,
      senderId: {
        _id: 'current_user', // This should be the current user's ID
        name: 'You',
        avatar: null
      },
      createdAt: new Date().toISOString(),
      status: {
        delivered: [],
        seen: []
      },
      isOptimistic: true,
      sending: true // Add sending state
    };

    // Add optimistic message to UI
    addOptimisticMessage(optimisticMessage);

    // Send message via socket
    try {
      socket.sendMessage(chatId, text.trim(), tempId);
      console.log('Message sent via socket:', tempId);
    } catch (error) {
      console.error('Failed to send message via socket:', error);
      // Mark the optimistic message as failed
      updateMessage(tempId, {
        error: true,
        errorMessage: 'Failed to send message',
        sending: false
      });
    }

    // Scroll to bottom
    if (messageListRef.current) {
      setTimeout(() => {
        messageListRef.current.scrollToBottom();
      }, 50);
    }
  };

  if (chatLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!chat) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center text-gray-500">
          <p>Chat not found</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col h-full">
      {/* Chat header */}
      <ChatHeader 
        chat={chat} 
        onBack={onBack}
        socket={socket}
      />

      {/* Messages */}
      <div className="flex-1 flex flex-col min-h-0">
        <MessageList
          ref={messageListRef}
          messages={messages}
          isLoading={messagesLoading}
          hasNextPage={hasNextPage}
          isFetchingNextPage={isFetchingNextPage}
          onLoadMore={loadMoreMessages}
          chatId={chatId}
          socket={socket}
        />
      </div>

      {/* Message input */}
      <MessageInput
        onSendMessage={handleSendMessage}
        disabled={!socket.isConnected}
        chatId={chatId}
        socket={socket}
      />
    </div>
  );
};
