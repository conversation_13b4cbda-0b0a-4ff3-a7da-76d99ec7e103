const User = require('../models/User');
const Chat = require('../models/Chat');
const Message = require('../models/Message');

// Store active typing users per chat
const typingUsers = new Map(); // chatId -> Set of userIds

/**
 * Main socket handler function
 * @param {SocketIO.Server} io - Socket.io server instance
 * @param {SocketIO.Socket} socket - Individual socket connection
 */
const socketHandlers = (io, socket) => {
  
  /**
   * Handle user connection
   */
  const handleConnection = async () => {
    try {
      console.log(`[${socket.userId}] Handling connection for socket ${socket.id}`);

      // Set user online status
      const user = await User.findById(socket.userId);
      if (!user) {
        console.error(`[${socket.userId}] User not found in database`);
        socket.emit('error', { message: 'User not found', code: 'USER_NOT_FOUND' });
        socket.disconnect();
        return;
      }

      console.log(`[${socket.userId}] Setting user online status`);
      await user.setOnline(socket.id);

      // Join user to their personal room for direct notifications
      socket.join(`user:${socket.userId}`);
      console.log(`[${socket.userId}] Joined personal room: user:${socket.userId}`);

      // Get user's chats and join chat rooms
      console.log(`[${socket.userId}] Finding user chats`);
      const userChats = await Chat.find({ 'members.user': socket.userId });
      console.log(`[${socket.userId}] Found ${userChats.length} chats`);

      for (const chat of userChats) {
        socket.join(`chat:${chat._id}`);
        console.log(`[${socket.userId}] Joined chat room: chat:${chat._id}`);
      }

      // Notify other users that this user is online
      socket.broadcast.emit('user:online', {
        userId: socket.userId,
        name: user.name,
        avatar: user.avatar
      });

      // Send connection success to client
      socket.emit('connection:success', {
        userId: socket.userId,
        chats: userChats.map(chat => chat._id)
      });

      console.log(`[${socket.userId}] User ${user.name} is now online and connected to ${userChats.length} chats`);

    } catch (error) {
      console.error(`[${socket.userId}] Connection handler error:`, {
        error: error.message,
        stack: error.stack
      });
      socket.emit('error', {
        message: 'Connection failed',
        code: 'CONNECTION_ERROR',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  };
  
  /**
   * Handle user disconnection
   */
  const handleDisconnection = async () => {
    try {
      // Clear typing status for all chats
      for (const [chatId, typingSet] of typingUsers.entries()) {
        if (typingSet.has(socket.userId)) {
          typingSet.delete(socket.userId);
          socket.to(`chat:${chatId}`).emit('typing:stop', {
            chatId,
            userId: socket.userId
          });
          
          if (typingSet.size === 0) {
            typingUsers.delete(chatId);
          }
        }
      }
      
      // Set user offline status
      const user = await User.findById(socket.userId);
      if (user) {
        await user.setOffline();
        
        // Notify other users that this user is offline
        socket.broadcast.emit('user:offline', {
          userId: socket.userId,
          lastSeen: new Date()
        });
        
        console.log(`User ${user.name} (${socket.userId}) is now offline`);
      }
    } catch (error) {
      console.error('Disconnection handler error:', error.message);
    }
  };
  
  /**
   * Handle joining a chat room
   */
  const handleJoinChat = async (data) => {
    try {
      const { chatId } = data;

      console.log(`[${socket.userId}] Attempting to join chat: ${chatId}`);

      if (!chatId) {
        console.log(`[${socket.userId}] Join chat failed: No chat ID provided`);
        socket.emit('error', { message: 'Chat ID is required', code: 'MISSING_CHAT_ID' });
        return;
      }

      // Verify user is a member of this chat
      console.log(`[${socket.userId}] Verifying membership for chat ${chatId}`);
      const chat = await Chat.findById(chatId);
      if (!chat) {
        console.log(`[${socket.userId}] Chat not found: ${chatId}`);
        socket.emit('error', { message: 'Chat not found', code: 'CHAT_NOT_FOUND' });
        return;
      }

      if (!chat.isMember(socket.userId)) {
        console.log(`[${socket.userId}] User not a member of chat ${chatId}`);
        socket.emit('error', { message: 'You are not a member of this chat', code: 'NOT_MEMBER' });
        return;
      }

      // Join the chat room
      socket.join(`chat:${chatId}`);
      console.log(`[${socket.userId}] Successfully joined chat room: chat:${chatId}`);

      // Mark messages as delivered
      try {
        await Message.markChatMessagesAsDelivered(chatId, socket.userId);
        console.log(`[${socket.userId}] Marked messages as delivered for chat ${chatId}`);
      } catch (deliveryError) {
        console.error(`[${socket.userId}] Failed to mark messages as delivered:`, deliveryError.message);
        // Don't fail the join operation for this
      }

      socket.emit('chat:joined', { chatId });
      console.log(`[${socket.userId}] Chat join confirmation sent for ${chatId}`);

    } catch (error) {
      console.error(`[${socket.userId}] Join chat error:`, {
        error: error.message,
        stack: error.stack,
        chatId: data?.chatId
      });
      socket.emit('error', {
        message: 'Failed to join chat',
        code: 'JOIN_ERROR',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  };
  
  /**
   * Handle leaving a chat room
   */
  const handleLeaveChat = (data) => {
    try {
      const { chatId } = data;
      
      if (!chatId) {
        socket.emit('error', { message: 'Chat ID is required' });
        return;
      }
      
      // Leave the chat room
      socket.leave(`chat:${chatId}`);
      
      // Stop typing if user was typing
      const typingSet = typingUsers.get(chatId);
      if (typingSet && typingSet.has(socket.userId)) {
        typingSet.delete(socket.userId);
        socket.to(`chat:${chatId}`).emit('typing:stop', {
          chatId,
          userId: socket.userId
        });
        
        if (typingSet.size === 0) {
          typingUsers.delete(chatId);
        }
      }
      
      socket.emit('chat:left', { chatId });
      
    } catch (error) {
      console.error('Leave chat error:', error.message);
      socket.emit('error', { message: 'Failed to leave chat' });
    }
  };
  
  /**
   * Handle sending a message
   */
  const handleSendMessage = async (data) => {
    try {
      const { chatId, text, type = 'text', tempId } = data;

      console.log(`[${socket.userId}] Attempting to send message to chat ${chatId}:`, { text: text?.substring(0, 50), type, tempId });

      // Validate input
      if (!chatId || !text || text.trim().length === 0) {
        console.log(`[${socket.userId}] Message validation failed:`, { chatId: !!chatId, text: !!text, textLength: text?.length });
        socket.emit('message:error', {
          message: 'Chat ID and message text are required',
          tempId,
          code: 'VALIDATION_ERROR'
        });
        return;
      }

      if (text.length > 4000) {
        console.log(`[${socket.userId}] Message too long:`, text.length);
        socket.emit('message:error', {
          message: 'Message text is too long (max 4000 characters)',
          tempId,
          code: 'MESSAGE_TOO_LONG'
        });
        return;
      }

      // Verify user is a member of this chat
      console.log(`[${socket.userId}] Checking chat membership for chat ${chatId}`);
      const chat = await Chat.findById(chatId);
      if (!chat) {
        console.log(`[${socket.userId}] Chat not found:`, chatId);
        socket.emit('message:error', {
          message: 'Chat not found',
          tempId,
          code: 'CHAT_NOT_FOUND'
        });
        return;
      }

      if (!chat.isMember(socket.userId)) {
        console.log(`[${socket.userId}] User not a member of chat ${chatId}. Members:`, chat.members.map(m => m.user));
        socket.emit('message:error', {
          message: 'You are not a member of this chat',
          tempId,
          code: 'NOT_MEMBER'
        });
        return;
      }

      console.log(`[${socket.userId}] Creating message for chat ${chatId}`);

      // Create and save message
      const message = new Message({
        text: text.trim(),
        type,
        chatId,
        senderId: socket.userId
      });

      console.log(`[${socket.userId}] Saving message to database`);
      await message.save();
      console.log(`[${socket.userId}] Message saved with ID:`, message._id);

      await message.populate('senderId', 'name avatar');
      console.log(`[${socket.userId}] Message populated with sender info`);

      // Update chat's last message and activity
      chat.lastMessage = message._id;
      await chat.updateLastActivity();
      console.log(`[${socket.userId}] Chat last message updated`);

      // Emit message to all chat members
      const messageData = {
        message: message.toObject(),
        tempId // Include tempId for optimistic updates
      };

      console.log(`[${socket.userId}] Broadcasting message to chat:${chatId}`, messageData.message._id);
      io.to(`chat:${chatId}`).emit('message:new', messageData);

      // Also emit success confirmation to sender
      socket.emit('message:sent', {
        messageId: message._id,
        tempId,
        timestamp: message.createdAt
      });

      // Mark as delivered for online chat members
      const onlineMembers = await User.find({
        _id: { $in: chat.members.map(m => m.user) },
        isOnline: true,
        _id: { $ne: socket.userId } // Exclude sender
      });

      console.log(`[${socket.userId}] Found ${onlineMembers.length} online members for delivery`);

      for (const member of onlineMembers) {
        await message.markAsDelivered(member._id);
      }

      // Emit delivery status
      if (onlineMembers.length > 0) {
        io.to(`chat:${chatId}`).emit('message:delivered', {
          messageId: message._id,
          deliveredTo: onlineMembers.map(m => m._id)
        });
        console.log(`[${socket.userId}] Message delivery status emitted`);
      }

    } catch (error) {
      console.error(`[${socket.userId}] Send message error:`, {
        error: error.message,
        stack: error.stack,
        chatId: data?.chatId,
        tempId: data?.tempId
      });

      socket.emit('message:error', {
        message: 'Failed to send message',
        tempId: data?.tempId,
        code: 'SERVER_ERROR',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  };
  
  /**
   * Handle marking messages as seen
   */
  const handleMarkSeen = async (data) => {
    try {
      const { chatId, messageIds } = data;
      
      if (!chatId) {
        socket.emit('error', { message: 'Chat ID is required' });
        return;
      }
      
      // Verify user is a member of this chat
      const chat = await Chat.findById(chatId);
      if (!chat || !chat.isMember(socket.userId)) {
        socket.emit('error', { message: 'You are not a member of this chat' });
        return;
      }
      
      if (messageIds && Array.isArray(messageIds)) {
        // Mark specific messages as seen
        for (const messageId of messageIds) {
          const message = await Message.findById(messageId);
          if (message && message.chatId.toString() === chatId) {
            await message.markAsSeen(socket.userId);
          }
        }
      } else {
        // Mark all messages in chat as seen
        await Message.markChatMessagesAsSeen(chatId, socket.userId);
      }
      
      // Emit seen status to chat members
      socket.to(`chat:${chatId}`).emit('message:seen', {
        chatId,
        userId: socket.userId,
        messageIds: messageIds || 'all'
      });
      
    } catch (error) {
      console.error('Mark seen error:', error.message);
      socket.emit('error', { message: 'Failed to mark messages as seen' });
    }
  };
  
  /**
   * Handle typing indicators
   */
  const handleTypingStart = (data) => {
    try {
      const { chatId } = data;
      
      if (!chatId) {
        socket.emit('error', { message: 'Chat ID is required' });
        return;
      }
      
      // Add user to typing set
      if (!typingUsers.has(chatId)) {
        typingUsers.set(chatId, new Set());
      }
      typingUsers.get(chatId).add(socket.userId);
      
      // Emit typing start to other chat members
      socket.to(`chat:${chatId}`).emit('typing:start', {
        chatId,
        userId: socket.userId,
        userName: socket.userName
      });
      
    } catch (error) {
      console.error('Typing start error:', error.message);
    }
  };
  
  const handleTypingStop = (data) => {
    try {
      const { chatId } = data;
      
      if (!chatId) {
        socket.emit('error', { message: 'Chat ID is required' });
        return;
      }
      
      // Remove user from typing set
      const typingSet = typingUsers.get(chatId);
      if (typingSet) {
        typingSet.delete(socket.userId);
        
        if (typingSet.size === 0) {
          typingUsers.delete(chatId);
        }
      }
      
      // Emit typing stop to other chat members
      socket.to(`chat:${chatId}`).emit('typing:stop', {
        chatId,
        userId: socket.userId
      });
      
    } catch (error) {
      console.error('Typing stop error:', error.message);
    }
  };
  
  // Register event handlers
  socket.on('chat:join', handleJoinChat);
  socket.on('chat:leave', handleLeaveChat);
  socket.on('message:send', handleSendMessage);
  socket.on('message:seen', handleMarkSeen);
  socket.on('typing:start', handleTypingStart);
  socket.on('typing:stop', handleTypingStop);
  socket.on('disconnect', handleDisconnection);
  
  // Handle connection
  handleConnection();
};

module.exports = socketHandlers;
