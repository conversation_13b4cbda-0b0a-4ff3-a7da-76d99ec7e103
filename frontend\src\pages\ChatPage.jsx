import React, { useState, useEffect } from 'react';
import { Routes, Route, useParams, useNavigate } from 'react-router-dom';
import { useSocket } from '../contexts/SocketContext';
import { useChats } from '../hooks/useChats';
import { ChatSidebar } from '../components/chat/ChatSidebar';
import { ChatWindow } from '../components/chat/ChatWindow';
import { WelcomeScreen } from '../components/chat/WelcomeScreen';
import { LoadingSpinner } from '../components/ui/LoadingSpinner';

export const ChatPage = () => {
  const navigate = useNavigate();
  const { chats, isLoading: chatsLoading, updateChatLastMessage } = useChats();
  const socket = useSocket();
  const [selectedChatId, setSelectedChatId] = useState(null);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Handle real-time message updates
  useEffect(() => {
    if (!socket.isConnected) return;

    const handleNewMessage = (data) => {
      const { message } = data;
      
      // Update chat's last message
      updateChatLastMessage(message.chatId, message);
      
      // If this is not the currently selected chat, we might want to show a notification
      if (message.chatId !== selectedChatId) {
        // TODO: Show notification or update unread count
      }
    };

    const handleMessageDelivered = (data) => {
      // TODO: Update message delivery status in UI
      console.log('Message delivered:', data);
    };

    const handleMessageSeen = (data) => {
      // TODO: Update message seen status in UI
      console.log('Message seen:', data);
    };

    // Register socket event listeners
    socket.addEventListener('message:new', handleNewMessage);
    socket.addEventListener('message:delivered', handleMessageDelivered);
    socket.addEventListener('message:seen', handleMessageSeen);

    return () => {
      socket.removeEventListener('message:new', handleNewMessage);
      socket.removeEventListener('message:delivered', handleMessageDelivered);
      socket.removeEventListener('message:seen', handleMessageSeen);
    };
  }, [socket, selectedChatId, updateChatLastMessage]);

  // Handle chat selection from URL
  const ChatRoute = () => {
    const { chatId } = useParams();
    
    useEffect(() => {
      if (chatId && chatId !== selectedChatId) {
        setSelectedChatId(chatId);
        setIsMobileMenuOpen(false); // Close mobile menu when chat is selected
      }
    }, [chatId]);

    return chatId ? (
      <ChatWindow 
        chatId={chatId} 
        onBack={() => {
          setIsMobileMenuOpen(true);
          navigate('/chat');
        }}
      />
    ) : (
      <WelcomeScreen />
    );
  };

  const handleChatSelect = (chatId) => {
    setSelectedChatId(chatId);
    navigate(`/chat/${chatId}`);
    setIsMobileMenuOpen(false);
  };

  const handleNewChat = () => {
    setIsMobileMenuOpen(false);
    // TODO: Open new chat modal or navigate to user search
  };

  if (chatsLoading) {
    return (
      <div className="h-screen flex items-center justify-center bg-gray-50">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="h-screen flex bg-gray-50">
      {/* Sidebar */}
      <div className={`
        ${isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'}
        lg:translate-x-0 lg:static lg:inset-0
        fixed inset-y-0 left-0 z-50
        w-80 bg-white border-r border-gray-200
        transition-transform duration-300 ease-in-out
      `}>
        <ChatSidebar
          chats={chats}
          selectedChatId={selectedChatId}
          onChatSelect={handleChatSelect}
          onNewChat={handleNewChat}
          socket={socket}
        />
      </div>

      {/* Mobile overlay */}
      {isMobileMenuOpen && (
        <div
          className="lg:hidden fixed inset-0 z-40 bg-black bg-opacity-50"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}

      {/* Main content */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* Mobile header */}
        <div className="lg:hidden bg-white border-b border-gray-200 px-4 py-3">
          <button
            onClick={() => setIsMobileMenuOpen(true)}
            className="p-2 rounded-lg hover:bg-gray-100"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>

        {/* Chat content */}
        <div className="flex-1 flex flex-col">
          <Routes>
            <Route path="/" element={<WelcomeScreen />} />
            <Route path="/:chatId" element={<ChatRoute />} />
          </Routes>
        </div>
      </div>

      {/* Connection status indicator */}
      {!socket.isConnected && (
        <div className="fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
            <span className="text-sm">Reconnecting...</span>
          </div>
        </div>
      )}
    </div>
  );
};
