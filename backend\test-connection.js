const mongoose = require('mongoose');
require('dotenv').config();

async function testConnection() {
  try {
    console.log('🔌 Testing MongoDB connection...');
    console.log('URI:', process.env.MONGODB_URI ? 'Found' : 'Missing');
    
    const conn = await mongoose.connect(process.env.MONGODB_URI, {
      serverSelectionTimeoutMS: 5000, // 5 second timeout
    });
    
    console.log('✅ Connected to MongoDB:', conn.connection.host);
    
    // Test basic operations
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log('✅ Found collections:', collections.map(c => c.name));
    
    await mongoose.connection.close();
    console.log('📦 Connection closed');
    
  } catch (error) {
    console.error('❌ Connection failed:', error.message);
  }
}

testConnection();
